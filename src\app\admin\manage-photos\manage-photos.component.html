<div class="admin-container" [@fadeIn]>
  <!-- Header -->
  <div class="admin-header">
    <h1>Product Management</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="toggleAddForm()" class="add-button">
        <mat-icon>add</mat-icon>
        Add Product
      </button>
      <button mat-button color="warn" (click)="logout()" class="logout-button">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </div>
  </div>

  <!-- Add/Edit Form -->
  <div class="form-container" *ngIf="showAddForm" [@slideIn]>
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>
          {{ editingProduct ? 'Edit Product' : 'Add New Product' }}
        </mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="productForm" (ngSubmit)="onSubmit()" class="product-form">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Product Name</mat-label>
              <input matInput formControlName="name">
              <mat-error *ngIf="name?.invalid && name?.touched">
                <span *ngIf="name?.errors?.['required']">Product name is required</span>
                <span *ngIf="name?.errors?.['minlength']">Name must be at least 2 characters</span>
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Caption</mat-label>
              <textarea matInput formControlName="caption" rows="3"></textarea>
              <mat-error *ngIf="caption?.invalid && caption?.touched">
                <span *ngIf="caption?.errors?.['required']">Caption is required</span>
                <span *ngIf="caption?.errors?.['minlength']">Caption must be at least 5 characters</span>
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <div class="file-upload-section">
              <label class="file-upload-label">Product Image</label>
              <input type="file" (change)="onFileSelected($event)" accept="image/*" class="file-input" #fileInput>
              <button type="button" mat-stroked-button (click)="fileInput.click()" class="upload-button">
                <mat-icon>cloud_upload</mat-icon>
                Choose Image
              </button>
            </div>
          </div>

          <div class="form-row" *ngIf="previewUrl">
            <div class="image-preview">
              <img [src]="previewUrl" alt="Preview" class="preview-image">
            </div>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Image URL</mat-label>
              <input matInput formControlName="imageUrl">
              <mat-error *ngIf="imageUrl?.invalid && imageUrl?.touched">
                Image URL is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-actions">
            <button type="submit" mat-raised-button color="primary" [disabled]="productForm.invalid">
              {{ editingProduct ? 'Update Product' : 'Add Product' }}
            </button>
            <button type="button" mat-button (click)="cancelEdit()">
              Cancel
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Products Grid -->
  <div class="products-section">
    <h2>Current Products ({{ products.length }})</h2>
    <div class="products-grid">
      <mat-card class="product-card" *ngFor="let product of products" [@slideIn]>
        <div class="product-image-container">
          <img [src]="product.imageUrl" [alt]="product.name" class="product-image">
        </div>

        <mat-card-content class="product-content">
          <h3 class="product-name">{{ product.name }}</h3>
          <p class="product-caption">{{ product.caption }}</p>
        </mat-card-content>

        <mat-card-actions class="product-actions">
          <button mat-icon-button color="primary" (click)="editProduct(product)" matTooltip="Edit">
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-icon-button color="warn" (click)="deleteProduct(product.id)" matTooltip="Delete">
            <mat-icon>delete</mat-icon>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <div class="empty-state" *ngIf="products.length === 0">
      <mat-icon class="empty-icon">inventory_2</mat-icon>
      <h3>No products found</h3>
      <p>Add your first product to get started</p>
    </div>
  </div>


</div>
