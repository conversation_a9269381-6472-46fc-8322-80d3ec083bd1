import { Injectable } from '@angular/core';
import { Observable, lastValueFrom } from 'rxjs';
import { AngularFireDatabase } from '@angular/fire/compat/database';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { map, finalize } from 'rxjs/operators';

export interface Product {
  id: string;
  name: string;
  caption: string;
  imageUrl: string;
}

@Injectable({ providedIn: 'root' })
export class ProductService {
  private productsPath = 'products';

  // Local fallback data for development
  private localProducts: Product[] = [
    {
      id: '1',
      name: 'Classic Sneaker',
      caption: 'Timeless comfort and style.',
      imageUrl: '/assets/images/Classic-Sneaker.jpeg'
    },
    {
      id: '2',
      name: 'Urban Boot',
      caption: 'Rugged and ready for any adventure.',
      imageUrl: '/assets/images/urban-boot.jpg'
    },
    {
      id: '3',
      name: 'Summer Sandal',
      caption: 'Breezy comfort for warm days.',
      imageUrl: '/assets/images/Summer Sandal.jpeg'
    },
    {
      id: '4',
      name: 'Sport Runner',
      caption: 'Performance and support for every run.',
      imageUrl: '/assets/images/Sport Runner.jpg'
    }
  ];

  constructor(
    private db: AngularFireDatabase,
    private storage: AngularFireStorage
  ) {
    // Initialize Firebase with local data if empty
    this.initializeFirebaseData();
  }

  // Initialize Firebase with local data if database is empty
  private async initializeFirebaseData(): Promise<void> {
    try {
      const snapshot = await this.db.list(this.productsPath).query.once('value');
      if (!snapshot.exists()) {
        // Database is empty, populate with local data
        for (const product of this.localProducts) {
          await this.db.list(this.productsPath).push(product);
        }
      }
    } catch (error) {
      console.error('Error initializing Firebase data:', error);
    }
  }

  // Get all products from Firebase Realtime Database
  getProducts(): Observable<Product[]> {
    return this.db.list<Product>(this.productsPath).snapshotChanges().pipe(
      map(changes =>
        changes.map(c => {
          const data = c.payload.val() as Product;
          return {
            ...data,
            id: c.payload.key as string
          };
        })
      )
    );
  }

  // Add product to Firebase Realtime Database
  async addProduct(product: Product): Promise<void> {
    try {
      const productData = { ...product };
      delete (productData as any).id; // Remove id as Firebase will generate the key
      await this.db.list(this.productsPath).push(productData);
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  }

  // Update product in Firebase Realtime Database
  async updateProduct(updatedProduct: Product): Promise<void> {
    try {
      const productData = { ...updatedProduct };
      delete (productData as any).id; // Remove id from data
      await this.db.object(`${this.productsPath}/${updatedProduct.id}`).update(productData);
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  // Delete product from Firebase Realtime Database
  async deleteProduct(productId: string): Promise<void> {
    try {
      await this.db.object(`${this.productsPath}/${productId}`).remove();
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  // Get product by ID from Firebase Realtime Database
  getProductById(id: string): Observable<Product | null> {
    return this.db.object<Product>(`${this.productsPath}/${id}`).valueChanges().pipe(
      map(product => product ? { ...product, id } : null)
    );
  }

  // Upload image to Firebase Storage and return download URL
  async uploadImage(file: File, productName: string): Promise<string> {
    try {
      const timestamp = Date.now();
      const fileName = `${productName}_${timestamp}_${file.name}`;
      const filePath = `product-images/${fileName}`;

      const fileRef = this.storage.ref(filePath);
      const uploadTask = this.storage.upload(filePath, file);

      // Wait for upload to complete
      await lastValueFrom(uploadTask.snapshotChanges().pipe(
        finalize(() => {
          // Upload completed
        })
      ));

      // Get download URL
      const downloadURL = await lastValueFrom(fileRef.getDownloadURL());
      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  // Delete image from Firebase Storage
  async deleteImage(imageUrl: string): Promise<void> {
    try {
      if (imageUrl && imageUrl.includes('firebase')) {
        const imageRef = this.storage.storage.refFromURL(imageUrl);
        await imageRef.delete();
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      // Don't throw error for image deletion as it's not critical
    }
  }

  // Add product with image upload
  async addProductWithImage(productData: Omit<Product, 'id'>, imageFile?: File): Promise<void> {
    try {
      let imageUrl = productData.imageUrl;

      if (imageFile) {
        imageUrl = await this.uploadImage(imageFile, productData.name);
      }

      const product = { ...productData, imageUrl };
      await this.addProduct(product as Product);
    } catch (error) {
      console.error('Error adding product with image:', error);
      throw error;
    }
  }

  // Update product with optional image upload
  async updateProductWithImage(productData: Product, imageFile?: File): Promise<void> {
    try {
      let imageUrl = productData.imageUrl;

      if (imageFile) {
        // Delete old image if it exists and is from Firebase
        if (productData.imageUrl && productData.imageUrl.includes('firebase')) {
          await this.deleteImage(productData.imageUrl);
        }

        // Upload new image
        imageUrl = await this.uploadImage(imageFile, productData.name);
      }

      const updatedProduct = { ...productData, imageUrl };
      await this.updateProduct(updatedProduct);
    } catch (error) {
      console.error('Error updating product with image:', error);
      throw error;
    }
  }

  // Delete product with image cleanup
  async deleteProductWithImage(productId: string, imageUrl?: string): Promise<void> {
    try {
      // Delete image first if it exists
      if (imageUrl) {
        await this.deleteImage(imageUrl);
      }

      // Then delete product data
      await this.deleteProduct(productId);
    } catch (error) {
      console.error('Error deleting product with image:', error);
      throw error;
    }
  }
}