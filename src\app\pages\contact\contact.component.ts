import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { trigger, transition, style, animate } from '@angular/animations';
import emailjs from 'emailjs-com';
import { EmailJSConfig } from './emailjs-config';

@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(40px)' }),
        animate('700ms 100ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ])
  ]
})
export class ContactComponent {
  contactForm: FormGroup;
  isLoading = false;
  submitMessage = '';
  submitMessageType: 'success' | 'error' | '' = '';

  // EmailJS configuration
  private readonly EMAIL_SERVICE_ID = EmailJSConfig.SERVICE_ID;
  private readonly EMAIL_TEMPLATE_ID = EmailJSConfig.TEMPLATE_ID;
  private readonly EMAIL_PUBLIC_KEY = EmailJSConfig.PUBLIC_KEY;

  constructor(private fb: FormBuilder) {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      email: ['', [Validators.required, Validators.email]],
      query: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  async onSubmit(): Promise<void> {
    if (this.contactForm.valid && !this.isLoading) {
      this.isLoading = true;
      this.submitMessage = '';
      this.submitMessageType = '';

      try {
        const formData = this.contactForm.value;

        // Prepare template parameters for EmailJS
        const templateParams = {
          from_name: formData.name,
          from_email: formData.email,
          message: formData.query,
          to_name: 'Footwear Demo Team', // You can customize this
        };

        // Send email using EmailJS
        const response = await emailjs.send(
          this.EMAIL_SERVICE_ID,
          this.EMAIL_TEMPLATE_ID,
          templateParams,
          this.EMAIL_PUBLIC_KEY
        );

        console.log('Email sent successfully:', response);
        this.submitMessage = 'Thank you! Your message has been sent successfully. We\'ll get back to you soon.';
        this.submitMessageType = 'success';
        this.contactForm.reset();

      } catch (error) {
        console.error('Error sending email:', error);
        this.submitMessage = 'Sorry, there was an error sending your message. Please try again later.';
        this.submitMessageType = 'error';
      } finally {
        this.isLoading = false;
      }
    }
  }

  clearMessage(): void {
    this.submitMessage = '';
    this.submitMessageType = '';
  }
}
