import { Component } from '@angular/core';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(40px)' }),
        animate('700ms 100ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ]),
    trigger('bigTextAnim', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.9)' }),
        animate('900ms 200ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'scale(1)' }))
      ])
    ]),
    trigger('infoAnim', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('700ms 400ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ]),
    trigger('buttonAnim', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('500ms 600ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ])
  ]
})
export class HomeComponent {
  bigText = 'Welcome to the Biggest Footwear Shop';
  infoLines: string[] = [];

  private infoPool = [
    'Over 10,000+ styles in stock',
    'Exclusive brands and latest trends',
    'Free shipping on your first order',
    'Trusted by 100,000+ happy customers',
    'Eco-friendly and sustainable options',
    'Easy returns and 24/7 support',
    'Handpicked collections for every season',
    'Unbeatable prices and offers',
    'Shop for men, women, and kids',
    'Your comfort, our priority'
  ];

  constructor() {
    // Pick 3 random info lines
    this.infoLines = this.infoPool
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
  }
}
