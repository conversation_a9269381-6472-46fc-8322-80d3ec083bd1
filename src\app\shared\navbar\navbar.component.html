<mat-toolbar color="primary">
  <span class="logo">Footwear Store</span>
  <span class="spacer"></span>

  <!-- Desktop Navigation -->
  <div class="nav-links desktop-nav">
    <a mat-button routerLink="/" routerLinkActive="active-link" [routerLinkActiveOptions]="{ exact: true }">Home</a>
    <a mat-button routerLink="/about" routerLinkActive="active-link">About</a>
    <a mat-button routerLink="/products" routerLinkActive="active-link">Products</a>
    <a mat-button routerLink="/contact" routerLinkActive="active-link">Contact</a>
  </div>

  <!-- Mobile Hamburger Button -->
  <button mat-icon-button class="hamburger-button" (click)="toggleMenu()" [attr.aria-label]="isMenuOpen ? 'Close menu' : 'Open menu'">
    <mat-icon>{{ isMenuOpen ? 'close' : 'menu' }}</mat-icon>
  </button>
</mat-toolbar>

<!-- Mobile Navigation Menu -->
<div class="mobile-nav" [class.open]="isMenuOpen">
  <div class="mobile-nav-overlay" (click)="closeMenu()"></div>
  <nav class="mobile-nav-content">
    <a mat-button routerLink="/" routerLinkActive="active-link" [routerLinkActiveOptions]="{ exact: true }" (click)="closeMenu()">
      <mat-icon>home</mat-icon>
      <span>Home</span>
    </a>
    <a mat-button routerLink="/about" routerLinkActive="active-link" (click)="closeMenu()">
      <mat-icon>info</mat-icon>
      <span>About</span>
    </a>
    <a mat-button routerLink="/products" routerLinkActive="active-link" (click)="closeMenu()">
      <mat-icon>inventory</mat-icon>
      <span>Products</span>
    </a>
    <a mat-button routerLink="/contact" routerLinkActive="active-link" (click)="closeMenu()">
      <mat-icon>contact_mail</mat-icon>
      <span>Contact</span>
    </a>
  </nav>
</div>
