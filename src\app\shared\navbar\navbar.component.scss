.mat-toolbar {
  background: #181a20 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.25);
  padding: 0 1rem;
  min-height: 64px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1000;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  letter-spacing: 1px;
  color: #90caf9;
  white-space: nowrap;
}

.spacer {
  flex: 1 1 auto;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.desktop-nav a[mat-button] {
  font-size: 1rem;
  text-transform: uppercase;
  color: #fff !important;
  opacity: 0.85;
  transition: color 0.2s, opacity 0.2s;
  white-space: nowrap;
  padding: 0 12px;
}

.desktop-nav a[mat-button]:hover {
  opacity: 1;
  color: #90caf9 !important;
}

.desktop-nav a.active-link {
  font-weight: bold;
  border-bottom: 2px solid #90caf9;
  color: #90caf9 !important;
  opacity: 1;
}

/* <PERSON><PERSON> */
.hamburger-button {
  display: none;
  color: #fff !important;

  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.3s ease, opacity 0.3s ease;
  pointer-events: none;

  &.open {
    visibility: visible;
    opacity: 1;
    pointer-events: all;
  }
}

.mobile-nav-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.mobile-nav-content {
  position: absolute;
  top: 64px;
  right: 0;
  width: 280px;
  height: calc(100vh - 64px);
  background: #181a20;
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  transform: translateX(100%);
  transition: transform 0.3s ease;

  .mobile-nav.open & {
    transform: translateX(0);
  }
}

.mobile-nav-content a[mat-button] {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  margin: 0.25rem 0;
  color: #fff !important;
  opacity: 0.85;
  text-transform: none;
  font-size: 1rem;
  text-align: left;
  justify-content: flex-start;
  border-radius: 0;
  transition: all 0.2s ease;

  mat-icon {
    color: #90caf9;
    font-size: 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }

  span {
    font-weight: 500;
  }

  &:hover {
    background: rgba(144, 202, 249, 0.1);
    opacity: 1;
    color: #90caf9 !important;
  }

  &.active-link {
    background: rgba(144, 202, 249, 0.15);
    color: #90caf9 !important;
    opacity: 1;
    font-weight: 600;
    border-left: 3px solid #90caf9;
  }
}

/* Mobile and Tablet - Show Hamburger Menu */
@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .hamburger-button {
    display: flex;
  }

  .mat-toolbar {
    padding: 0 1rem;
    min-height: 64px;
  }

  .logo {
    font-size: 1.4rem;
  }
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .mat-toolbar {
    padding: 0 0.75rem;
  }

  .logo {
    font-size: 1.3rem;
  }

  .mobile-nav-content {
    width: 100%;
    right: 0;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .mobile-nav-content {
    width: 300px;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .mat-toolbar {
    padding: 0 1.5rem;
  }

  .logo {
    font-size: 1.5rem;
  }

  .mobile-nav-content {
    width: 320px;
  }
}

/* iPads and Small Laptops - Desktop Navigation */
@media (min-width: 769px) and (max-width: 1024px) {
  .mat-toolbar {
    padding: 0 2rem;
  }

  .logo {
    font-size: 1.5rem;
  }

  .desktop-nav {
    gap: 0.5rem;
  }

  .desktop-nav a[mat-button] {
    font-size: 1rem;
    padding: 0 12px;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .mat-toolbar {
    padding: 0 2.5rem;
  }

  .logo {
    font-size: 1.6rem;
  }

  .desktop-nav {
    gap: 0.75rem;
  }

  .desktop-nav a[mat-button] {
    font-size: 1rem;
    padding: 0 16px;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .mat-toolbar {
    padding: 0 3rem;
  }

  .logo {
    font-size: 1.7rem;
  }

  .desktop-nav {
    gap: 1rem;
  }

  .desktop-nav a[mat-button] {
    font-size: 1.05rem;
    padding: 0 18px;
  }
}
