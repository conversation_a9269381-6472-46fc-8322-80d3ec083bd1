import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AdminAuthService } from '../services/admin-auth.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-admin-login',
  templateUrl: './admin-login.component.html',
  styleUrls: ['./admin-login.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(40px)' }),
        animate('700ms 100ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ])
  ]
})
export class AdminLoginComponent {
  loginForm: FormGroup;
  loginError = '';
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private adminAuthService: AdminAuthService
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  async onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.loginError = '';

      const { email, password } = this.loginForm.value;

      try {
        const success = await this.adminAuthService.loginWithEmailPassword(email, password);

        if (success) {
          this.isLoading = false;
          this.router.navigate(['/admin/manage-photos']);
        } else {
          this.isLoading = false;
          this.loginError = 'Invalid email or password';
        }
      } catch (error: any) {
        this.isLoading = false;
        // Handle specific Firebase auth errors
        switch (error.code) {
          case 'auth/user-not-found':
            this.loginError = 'No user found with this email address';
            break;
          case 'auth/wrong-password':
            this.loginError = 'Incorrect password';
            break;
          case 'auth/invalid-email':
            this.loginError = 'Invalid email address';
            break;
          case 'auth/user-disabled':
            this.loginError = 'This account has been disabled';
            break;
          case 'auth/too-many-requests':
            this.loginError = 'Too many failed attempts. Please try again later';
            break;
          default:
            this.loginError = 'Login failed. Please try again';
        }
      }
    }
  }

  // Getter methods for form controls
  get email() { return this.loginForm.get('email'); }
  get password() { return this.loginForm.get('password'); }
}
