﻿.home-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 64px - 40px); // Subtract navbar (~64px) and footer (~40px)
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.bg-image {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  background: url('/assets/images/c2c371ac-e904-4dc0-be51-cb67e09fc5b0.png') center center/cover no-repeat;
  opacity: 0.18;
  z-index: 1;
  filter: blur(1.5px) brightness(0.9);
  pointer-events: none;
}

.bg-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: linear-gradient(180deg, rgba(0,0,0,0.45) 0%, rgba(0,0,0,0.65) 100%);
  pointer-events: none;
}

.home-content {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  box-sizing: border-box;
}

.big-welcome {
  font-size: 2.8rem;
  font-weight: 800;
  color: #fff;
  text-align: center;
  margin-bottom: 2.2rem;
  letter-spacing: 1.5px;
  line-height: 1.15;
  text-shadow: 0 2px 24px rgba(0,0,0,0.45);
}

.info-list {
  margin-bottom: 2.2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.7rem;
}

.info-line {
  font-size: 1.15rem;
  color: #e0e0e0;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  box-shadow: 0 1px 6px rgba(0,0,0,0.12);
  font-weight: 500;
}

.cta-row {
  display: flex;
  gap: 1.5rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.contact-link {
  font-size: 1rem;
  text-decoration: underline;
  color: #90caf9 !important;
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .home-container {
    height: calc(100vh - 120px - 35px); // Adjusted for mobile navbar and footer
    padding: 0.5rem;
  }

  .home-content {
    padding: 1rem 0.75rem;
    max-width: 100%;
    text-align: center;
  }

  .big-welcome {
    font-size: 1.6rem;
    margin-bottom: 1rem;
    line-height: 1.1;
    padding: 0 0.5rem;
  }

  .info-list {
    gap: 0.5rem;
    margin: 1rem 0;
  }

  .info-line {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
    margin: 0.25rem 0;
  }

  .cta-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
    margin-top: 1rem;

    button, a {
      width: 100%;
      max-width: 220px;
      text-align: center;
      font-size: 0.9rem;
    }
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .home-container {
    height: calc(100vh - 100px - 35px);
    padding: 0.75rem;
  }

  .home-content {
    padding: 1.25rem 1rem;
    max-width: 100%;
    text-align: center;
  }

  .big-welcome {
    font-size: 1.7rem;
    margin-bottom: 1.25rem;
    line-height: 1.15;
  }

  .info-line {
    font-size: 0.95rem;
    padding: 0.35rem 0.7rem;
  }

  .cta-row {
    flex-direction: column;
    gap: 0.875rem;
    align-items: center;

    button, a {
      width: 100%;
      max-width: 240px;
      text-align: center;
    }
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .home-container {
    height: calc(100vh - 80px - 40px);
    padding: 1rem;
  }

  .home-content {
    padding: 1.5rem 1.25rem;
    max-width: 90%;
    text-align: center;
  }

  .big-welcome {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .info-line {
    font-size: 1rem;
    padding: 0.4rem 0.8rem;
  }

  .cta-row {
    flex-direction: row;
    gap: 1.25rem;
    justify-content: center;
    flex-wrap: wrap;

    button, a {
      min-width: 160px;
      text-align: center;
    }
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .home-container {
    height: calc(100vh - 64px - 40px);
    padding: 1.5rem;
  }

  .home-content {
    padding: 2rem 1.5rem;
    max-width: 85%;
  }

  .big-welcome {
    font-size: 2.25rem;
    margin-bottom: 1.75rem;
    line-height: 1.25;
  }

  .info-line {
    font-size: 1.05rem;
    padding: 0.45rem 0.9rem;
  }

  .cta-row {
    gap: 1.5rem;
    margin-top: 1.75rem;
  }
}

/* Laptops and Desktops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .home-container {
    height: calc(100vh - 64px - 40px);
    padding: 2rem;
  }

  .home-content {
    padding: 2.5rem 2rem;
    max-width: 80%;
  }

  .big-welcome {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    line-height: 1.3;
  }

  .info-line {
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
  }

  .cta-row {
    gap: 2rem;
    margin-top: 2rem;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .home-container {
    height: calc(100vh - 64px - 40px);
    padding: 2.5rem;
  }

  .home-content {
    padding: 3rem 2.5rem;
    max-width: 75%;
  }

  .big-welcome {
    font-size: 2.75rem;
    margin-bottom: 2.25rem;
    line-height: 1.35;
  }

  .info-line {
    font-size: 1.15rem;
    padding: 0.55rem 1.1rem;
  }

  .cta-row {
    gap: 2.5rem;
    margin-top: 2.25rem;
  }
}

@media (max-width: 480px) {
  .home-container {
    height: calc(100vh - 90px - 30px); // Further adjusted for very small screens
  }

  .home-content {
    padding: 1rem 0.75rem;
  }

  .big-welcome {
    font-size: 1.5rem;
    margin-bottom: 1.2rem;
  }

  .info-line {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }
}
