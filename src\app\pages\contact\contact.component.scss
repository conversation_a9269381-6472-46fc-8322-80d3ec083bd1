.contact-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
}

// Heading styles
h1 {
  color: #90caf9 !important;
  text-align: center;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.contact-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.full-width {
  width: 100%;
}

// Submit button styles
button[type="submit"] {
  align-self: flex-end;
  min-width: 120px;
  background: #90caf9 !important;
  color: #23272f !important;
  font-weight: 600;
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: #64b5f6 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(144, 202, 249, 0.3);
  }

  &:disabled {
    background: #424242 !important;
    color: #757575 !important;
    cursor: not-allowed;
  }
}

// Form field container styles
.mat-mdc-form-field {
  background: #2e323b !important;
  border-radius: 8px !important;

  // Remove all outline borders
  .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border: none !important;
      border-width: 0 !important;
    }
  }

  // Remove focused state borders
  &.mdc-text-field--focused {
    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border: none !important;
        border-width: 0 !important;
      }
    }
  }

  // Remove hover state borders
  &:hover:not(.mdc-text-field--focused) {
    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border: none !important;
      }
    }
  }
}

// Input and label styles
.mat-mdc-input-element {
  color: #90caf9 !important;
  caret-color: #90caf9 !important;
}

// Floating label styles
.mat-mdc-form-field {
  // Floating label when not focused (placeholder state)
  .mat-mdc-floating-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  // Floating label when focused or has value
  &.mat-focused .mat-mdc-floating-label,
  &.mat-form-field-should-float .mat-mdc-floating-label {
    color: #90caf9 !important;
  }
}

// Legacy label support
.mat-mdc-form-field-label {
  color: rgba(255, 255, 255, 0.7) !important;
}

.mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {
  color: #90caf9 !important;
}

// Placeholder styles
.mat-mdc-input-element::placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element::-webkit-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element::-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element:-ms-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element:-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

// Error message styles
.mat-mdc-form-field-error {
  color: #ff8a80 !important;
  font-size: 0.875rem;
}

// Hint styles
.mat-mdc-form-field-hint {
  color: rgba(144, 202, 249, 0.7) !important;
  font-size: 0.875rem;
}

// Textarea specific styles
textarea.mat-mdc-input-element {
  color: #90caf9 !important;
  resize: vertical;
  min-height: 120px;
}

// Legacy Material styles (fallback) - Remove borders
.mat-form-field-appearance-outline .mat-form-field-outline {
  border: none !important;
  color: transparent !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick {
  border: none !important;
  color: transparent !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-start,
.mat-form-field-appearance-outline .mat-form-field-outline-end {
  border: none !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-gap {
  border: none !important;
}

.mat-input-element, .mat-form-field-label {
  color: #90caf9 !important;
}

.mat-form-field.mat-focused .mat-form-field-label {
  color: #90caf9 !important;
}

.mat-form-field.mat-focused .mat-form-field-ripple {
  background-color: #90caf9 !important;
}

.mat-error {
  color: #ff8a80 !important;
}

.mat-hint {
  color: rgba(144, 202, 249, 0.7) !important;
}

.mat-label {
  color: #90caf9 !important;
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .contact-container {
    max-width: 100%;
    padding: 1rem 0.75rem 2rem 0.75rem;
  }

  h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .contact-form {
    gap: 1.25rem;
    margin-top: 1.25rem;
  }

  .mat-mdc-form-field {
    font-size: 0.9rem;
  }

  .mat-mdc-input-element {
    font-size: 0.9rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 100px;
  }

  button[type="submit"] {
    font-size: 0.9rem;
    padding: 0.75rem 1.5rem;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .contact-container {
    max-width: 100%;
    padding: 1.25rem 1rem 2.5rem 1rem;
  }

  h1 {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .contact-form {
    gap: 1.375rem;
    margin-top: 1.375rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 110px;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .contact-container {
    max-width: 550px;
    padding: 1.5rem 1.25rem 2.75rem 1.25rem;
  }

  h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .contact-form {
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 120px;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .contact-container {
    max-width: 580px;
    padding: 1.75rem 1.5rem 3rem 1.5rem;
  }

  h1 {
    font-size: 2.25rem;
    margin-bottom: 1.75rem;
  }

  .contact-form {
    gap: 1.75rem;
    margin-top: 1.75rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 130px;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .contact-container {
    max-width: 600px;
    padding: 2rem 1.5rem 3rem 1.5rem;
  }

  h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .contact-form {
    gap: 2rem;
    margin-top: 2rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 140px;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .contact-container {
    max-width: 650px;
    padding: 2.5rem 2rem 3.5rem 2rem;
  }

  h1 {
    font-size: 2.75rem;
    margin-bottom: 2.25rem;
  }

  .contact-form {
    gap: 2.25rem;
    margin-top: 2.25rem;
  }

  .mat-mdc-form-field {
    font-size: 1.05rem;
  }

  .mat-mdc-input-element {
    font-size: 1.05rem;
  }

  textarea.mat-mdc-input-element {
    min-height: 150px;
  }

  button[type="submit"] {
    font-size: 1.05rem;
    padding: 1rem 2rem;
  }
}