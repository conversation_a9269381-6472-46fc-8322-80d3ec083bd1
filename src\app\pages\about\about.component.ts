import { Component } from '@angular/core';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss']
})
export class AboutComponent {
  public timeline = [
    {
      title: '2010: The Dream Begins',
      text: 'Our founder, <PERSON><PERSON>, started Footwear Haven in a small garage with a passion for stylish, comfortable shoes.'
    },
    {
      title: '2013: First Store Opens',
      text: 'With growing demand, we opened our first retail store in Pune, India, bringing our unique designs to the public.'
    },
    {
      title: '2018: National Recognition',
      text: 'Footwear Haven was featured in top fashion magazines and expanded to multiple cities across India.'
    },
    {
      title: '2023: Embracing Sustainability',
      text: 'We launched our eco-friendly line, using recycled materials and ethical manufacturing.'
    }
  ];

  public locations = [
    { city: 'Pune', address: '123 MG Road, Pune, Maharashtra' },
    { city: 'Mumbai', address: '456 Bandra West, Mumbai, Maharashtra' },
    { city: 'Bangalore', address: '789 Indiranagar, Bangalore, Karnataka' }
  ];
}
