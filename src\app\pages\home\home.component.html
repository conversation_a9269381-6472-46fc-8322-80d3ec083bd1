<div class="home-container" [@fadeIn]>
  <div class="bg-image"></div>
  <div class="bg-overlay"></div>
  <div class="home-content">
    <h1 class="big-welcome" [@bigTextAnim]>{{ bigText }}</h1>
    <div class="info-list" [@infoAnim]>
      <div class="info-line" *ngFor="let info of infoLines">{{ info }}</div>
    </div>
    <div class="cta-row">
      <button mat-raised-button color="primary" [@buttonAnim] routerLink="/products">Check Our Products</button>
      <a mat-button color="primary" routerLink="/contact" class="contact-link">Contact Us</a>
    </div>
  </div>
</div>
