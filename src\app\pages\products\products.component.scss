﻿.products-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  width: 100%;
  margin-top: 2rem;
  justify-items: center;
}

.product-card {
  max-width: 320px;
  min-width: 220px;
  margin: 0 auto;
  box-shadow: 0 2px 12px rgba(0,0,0,0.25);
  transition: transform 0.2s cubic-bezier(.4,2,.6,1);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #23272f !important;
  color: #e0e0e0 !important;
}

.product-card:hover {
  transform: translateY(-6px) scale(1.04);
  box-shadow: 0 6px 24px rgba(25, 118, 210, 0.13);
}

img[mat-card-image] {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 12px 12px 0 0;
  background: #181a20;
  height: 240px;
  transition: opacity 0.3s ease;

  // Loading state
  &:not([src]), &[src=""] {
    opacity: 0.5;
    background: #181a20 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2390caf9"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>') center center/50px no-repeat;
  }

  // Error state
  &[src*="placeholder"] {
    background: #181a20 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff8a80"><path d="M21 5v6.59l-3-3.01-4 4.01-4-4-4 4-3-3.01V5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2zm-3 6.42l3 3.01V19c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6.58l3 2.99 4-4 4 4 4-3.99z"/></svg>') center center/50px no-repeat;
    opacity: 0.7;
  }
}

mat-card-title {
  color: #90caf9;
  font-weight: 600;
  font-size: 1.15rem;
  margin: 0.5rem 0 0.2rem 0;
  padding: 0 0.7rem;
}

mat-card-content p {
  color: #b0b0b0;
  font-size: 1.05rem;
  margin: 0 0 0.7rem 0;
  padding: 0 0.7rem;
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .products-container {
    padding: 1rem 0.75rem 2rem 0.75rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .product-card {
    max-width: 100%;
    min-width: 0;
    margin: 0;
  }

  img[mat-card-image] {
    height: 180px;
  }

  mat-card-title {
    font-size: 1rem;
    padding: 0 0.5rem;
  }

  mat-card-content p {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .products-container {
    padding: 1.25rem 1rem 2.5rem 1rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
    margin-top: 1.75rem;
  }

  .product-card {
    max-width: 100%;
    min-width: 180px;
  }

  img[mat-card-image] {
    height: 160px;
  }

  mat-card-title {
    font-size: 1.05rem;
  }

  mat-card-content p {
    font-size: 0.95rem;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .products-container {
    padding: 1.5rem 1.25rem 2.75rem 1.25rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .product-card {
    max-width: 280px;
    min-width: 200px;
  }

  img[mat-card-image] {
    height: 180px;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .products-container {
    padding: 1.75rem 1.5rem 3rem 1.5rem;
    max-width: 1000px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.75rem;
    margin-top: 2rem;
  }

  .product-card {
    max-width: 300px;
    min-width: 220px;
  }

  img[mat-card-image] {
    height: 200px;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .products-container {
    padding: 2rem 1.5rem 3rem 1.5rem;
    max-width: 1100px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .product-card {
    max-width: 320px;
    min-width: 240px;
  }

  img[mat-card-image] {
    height: 220px;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .products-container {
    padding: 2.5rem 2rem 3.5rem 2rem;
    max-width: 1300px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 2.5rem;
  }

  .product-card {
    max-width: 350px;
    min-width: 260px;
  }

  img[mat-card-image] {
    height: 240px;
  }

  mat-card-title {
    font-size: 1.2rem;
  }

  mat-card-content p {
    font-size: 1.1rem;
  }
}
