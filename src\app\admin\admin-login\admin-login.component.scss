.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px - 40px);
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #181a20 0%, #23272f 100%);
  width: 100%;
  box-sizing: border-box;
}


.login-card {
  background: #23272f;
  border-radius: 16px;
  padding: 3rem 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 400px;
  border: 1px solid rgba(36, 52, 65, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: #90caf9;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #b0b0b0;
    font-size: 1rem;
    margin: 0;
  }
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.full-width {
  width: 100%;
}

.mat-mdc-form-field {
  .mdc-text-field--outlined {
    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(144, 202, 249, 0.3) !important;
      }
    }
    
    &:hover .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(144, 202, 249, 0.6) !important;
      }
    }
    
    &.mdc-text-field--focused .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #90caf9 !important;
        border-width: 2px !important;
      }
    }
  }
}

.error-message {
  color: #ff8a80;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: rgba(255, 138, 128, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 138, 128, 0.3);
}

.login-button {
  background: #90caf9 !important;
  color: #181a20 !important;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  margin-top: 1rem;
  
  &:disabled {
    background: rgba(144, 202, 249, 0.3) !important;
    color: rgba(24, 26, 32, 0.5) !important;
  }
}
  
  code {
    background: rgba(144, 202, 249, 0.1);
    color: #90caf9;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
  }


/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .login-container {
    min-height: calc(100vh - 120px - 35px);
    padding: 1rem 0.75rem;
  }

  .login-card {
    padding: 1.5rem 1.25rem;
    margin: 0.5rem;
    max-width: 100%;
    border-radius: 12px;
  }

  .login-header {
    margin-bottom: 1.5rem;

    h1 {
      font-size: 1.4rem;
      margin-bottom: 0.375rem;
    }

    p {
      font-size: 0.9rem;
    }
  }

  .login-form {
    gap: 1.25rem;
  }

  .login-button {
    padding: 0.625rem 1.5rem;
    font-size: 0.9rem;
    margin-top: 0.75rem;
  }

  .error-message {
    font-size: 0.8rem;
    padding: 0.375rem;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .login-container {
    min-height: calc(100vh - 100px - 35px);
    padding: 1.25rem 1rem;
  }

  .login-card {
    padding: 1.75rem 1.5rem;
    margin: 0.75rem;
    max-width: 420px;
  }

  .login-header {
    margin-bottom: 1.75rem;

    h1 {
      font-size: 1.6rem;
      margin-bottom: 0.4rem;
    }

    p {
      font-size: 0.95rem;
    }
  }

  .login-form {
    gap: 1.375rem;
  }

  .login-button {
    padding: 0.7rem 1.75rem;
    font-size: 0.95rem;
    margin-top: 0.875rem;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .login-container {
    min-height: calc(100vh - 80px - 40px);
    padding: 1.5rem 1.25rem;
  }

  .login-card {
    padding: 2.25rem 2rem;
    margin: 1rem;
    max-width: 450px;
  }

  .login-header {
    margin-bottom: 2rem;

    h1 {
      font-size: 1.8rem;
      margin-bottom: 0.45rem;
    }
  }

  .login-form {
    gap: 1.5rem;
  }

  .login-button {
    padding: 0.75rem 2rem;
    margin-top: 1rem;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .login-container {
    padding: 1.75rem 1.5rem;
  }

  .login-card {
    padding: 2.5rem 2.25rem;
    max-width: 480px;
  }

  .login-header {
    margin-bottom: 2.25rem;

    h1 {
      font-size: 1.9rem;
      margin-bottom: 0.5rem;
    }
  }

  .login-form {
    gap: 1.75rem;
  }

  .login-button {
    padding: 0.8rem 2.25rem;
    margin-top: 1.25rem;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .login-container {
    padding: 2rem 1.5rem;
  }

  .login-card {
    padding: 3rem 2.5rem;
    max-width: 500px;
  }

  .login-header {
    margin-bottom: 2.5rem;

    h1 {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }
  }

  .login-form {
    gap: 2rem;
  }

  .login-button {
    padding: 0.875rem 2.5rem;
    margin-top: 1.5rem;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .login-container {
    padding: 2.5rem 2rem;
  }

  .login-card {
    padding: 3.5rem 3rem;
    max-width: 550px;
  }

  .login-header {
    margin-bottom: 3rem;

    h1 {
      font-size: 2.25rem;
      margin-bottom: 0.6rem;
    }

    p {
      font-size: 1.05rem;
    }
  }

  .login-form {
    gap: 2.25rem;
  }

  .login-button {
    padding: 1rem 3rem;
    font-size: 1.05rem;
    margin-top: 1.75rem;
  }

  .error-message {
    font-size: 0.95rem;
    padding: 0.6rem;
  }
}
