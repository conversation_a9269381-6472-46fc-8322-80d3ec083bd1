# Contact Form with EmailJS Integration

This contact form is integrated with EmailJS to send emails directly from the frontend without requiring a backend server.

## Setup Instructions

### 1. EmailJS Account Setup

1. Go to [EmailJS](https://www.emailjs.com/) and create a free account
2. Verify your email address

### 2. Create Email Service

1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, Yahoo, etc.)
4. Follow the setup instructions for your chosen provider
5. Note down the **Service ID** (e.g., `service_abc123`)

### 3. Create Email Template

1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Set up your template with these variables:
   - `{{from_name}}` - Sender's name
   - `{{from_email}}` - Sender's email address
   - `{{message}}` - The message content
   - `{{to_name}}` - Your name/company name

#### Example Template:

**Subject:** `New Contact Form Submission from {{from_name}}`

**Body:**
```
Hello {{to_name}},

You have received a new message from your website contact form:

Name: {{from_name}}
Email: {{from_email}}

Message:
{{message}}

Best regards,
Your Website Contact Form
```

4. Save the template and note down the **Template ID** (e.g., `template_xyz789`)

### 4. Get Public Key

1. Go to "Account" in your EmailJS dashboard
2. Find your **Public Key** (also called User ID) (e.g., `user_def456`)

### 5. Update Configuration

1. Open `src/app/pages/contact/emailjs-config.ts`
2. Replace the placeholder values with your actual EmailJS credentials:

```typescript
export const EmailJSConfig = {
  SERVICE_ID: 'your_actual_service_id',
  TEMPLATE_ID: 'your_actual_template_id',
  PUBLIC_KEY: 'your_actual_public_key'
};
```

### 6. Test the Integration

1. Start your Angular development server: `ng serve`
2. Navigate to the contact page
3. Fill out and submit the form
4. Check your email to confirm the message was received

## Features

- ✅ Form validation (required fields, email format)
- ✅ Loading spinner during email sending
- ✅ Success/error message display
- ✅ Form reset after successful submission
- ✅ Responsive design for all devices
- ✅ Character limits with counters
- ✅ Disabled form fields during submission

## Troubleshooting

### Common Issues:

1. **"Failed to send email"**
   - Check that your EmailJS credentials are correct
   - Verify your email service is properly configured
   - Check browser console for detailed error messages

2. **Template variables not working**
   - Ensure variable names in template match exactly: `{{from_name}}`, `{{from_email}}`, `{{message}}`, `{{to_name}}`
   - Check for typos in variable names

3. **Emails not being received**
   - Check spam/junk folder
   - Verify the "To Email" address in your EmailJS service configuration
   - Test the template directly in EmailJS dashboard

### Testing in EmailJS Dashboard:

1. Go to your template in EmailJS dashboard
2. Click "Test it" button
3. Fill in test values for all variables
4. Send test email to verify everything works

## Security Notes

- EmailJS public key is safe to expose in frontend code
- Rate limiting is handled by EmailJS (free tier: 200 emails/month)
- Consider implementing additional client-side rate limiting for production use
