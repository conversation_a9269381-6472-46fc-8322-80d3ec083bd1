<div class="login-container" [@fadeIn]>
  <div class="login-card">
    <div class="login-header">
      <h1>Admin Login</h1>
      <p>Access the product management system</p>
    </div>
    
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Email</mat-label>
        <input matInput type="email" formControlName="email">
        <mat-error *ngIf="email?.invalid && email?.touched">
          <span *ngIf="email?.errors?.['required']">Email is required</span>
          <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Password</mat-label>
        <input matInput type="password" formControlName="password">
        <mat-error *ngIf="password?.invalid && password?.touched">
          <span *ngIf="password?.errors?.['required']">Password is required</span>
          <span *ngIf="password?.errors?.['minlength']">Password must be at least 6 characters</span>
        </mat-error>
      </mat-form-field>

      <div class="error-message" *ngIf="loginError">
        {{ loginError }}
      </div>

      <button 
        mat-raised-button 
        color="primary" 
        type="submit" 
        class="login-button"
        [disabled]="loginForm.invalid || isLoading">
        <span *ngIf="!isLoading">Login</span>
        <span *ngIf="isLoading">Logging in...</span>
      </button>
    </form>

  </div>
</div>
