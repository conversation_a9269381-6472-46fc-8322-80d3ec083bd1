.about-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline {
  width: 100%;
  margin-bottom: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.timeline-card {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2.5rem;
  min-width: 250px;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.timeline-dot {
  position: absolute;
  left: 0.7rem;
  top: 1.2rem;
  width: 1.1rem;
  height: 1.1rem;
  background: #1976d2;
  border-radius: 50%;
  border: 3px solid #23272f;
  box-shadow: 0 0 0 2px #1976d2;
}

.timeline-content h2 {
  margin-top: 0;
  margin-bottom: 0.3rem;
  color: #90caf9;
}

.vision-mission {
  display: flex;
  gap: 2rem;
  width: 100%;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.vision-card, .mission-card {
  flex: 1 1 250px;
  min-width: 220px;
  max-width: 350px;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.locations {
  width: 100%;
  margin-top: 1.5rem;
}

.location-list {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 1rem;
}

.location-card {
  display: flex;
  align-items: flex-start;
  gap: 0.7rem;
  min-width: 200px;
  max-width: 300px;
  padding: 1rem 1.2rem;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.location-card mat-icon {
  color: #90caf9;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.loc-name {
  font-weight: 600;
  color: #fff;
}

.loc-address {
  font-size: 0.97rem;
  color: #b0b0b0;
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .about-container {
    padding: 1rem 0.75rem 2rem 0.75rem;
  }

  .timeline {
    margin-bottom: 2rem;
  }

  .timeline-card {
    padding-left: 2rem;
    margin-bottom: 1.5rem;
    padding-right: 0.75rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .timeline-dot {
    left: 0.5rem;
    top: 1rem;
    width: 0.9rem;
    height: 0.9rem;
  }

  .timeline-content h2 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .timeline-content p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .vision-mission {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .vision-card, .mission-card {
    min-width: 100%;
    max-width: 100%;
    margin: 0;
  }

  .vision-card mat-card-title,
  .mission-card mat-card-title {
    font-size: 1.1rem;
  }

  .vision-card mat-card-content,
  .mission-card mat-card-content {
    font-size: 0.9rem;
  }

  .location-list {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .location-card {
    min-width: 100%;
    max-width: 100%;
    padding: 0.875rem 1rem;
  }

  .location-card mat-icon {
    font-size: 1.75rem;
  }

  .loc-name {
    font-size: 0.95rem;
  }

  .loc-address {
    font-size: 0.85rem;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .about-container {
    padding: 1.25rem 1rem 2.5rem 1rem;
  }

  .timeline-card {
    padding-left: 2.25rem;
    margin-bottom: 1.75rem;
  }

  .timeline-dot {
    left: 0.6rem;
    width: 1rem;
    height: 1rem;
  }

  .vision-mission {
    flex-direction: column;
    gap: 1.25rem;
    margin-bottom: 2.25rem;
  }

  .vision-card, .mission-card {
    min-width: 100%;
    max-width: 100%;
  }

  .location-list {
    flex-direction: column;
    gap: 1.25rem;
    align-items: center;
  }

  .location-card {
    min-width: 100%;
    max-width: 100%;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .about-container {
    padding: 1.5rem 1.25rem 2.75rem 1.25rem;
  }

  .vision-mission {
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
  }

  .vision-card, .mission-card {
    min-width: 100%;
    max-width: 100%;
  }

  .location-list {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .location-card {
    min-width: 80%;
    max-width: 400px;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .about-container {
    padding: 1.75rem 1.5rem 3rem 1.5rem;
    max-width: 900px;
  }

  .vision-mission {
    gap: 1.75rem;
    margin-bottom: 2.5rem;
  }

  .vision-card, .mission-card {
    flex: 1 1 300px;
    min-width: 280px;
    max-width: 400px;
  }

  .location-list {
    gap: 1.75rem;
    justify-content: space-around;
  }

  .location-card {
    min-width: 250px;
    max-width: 320px;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .about-container {
    padding: 2rem 1.5rem 3rem 1.5rem;
    max-width: 950px;
  }

  .vision-mission {
    gap: 2rem;
    margin-bottom: 2.5rem;
  }

  .vision-card, .mission-card {
    flex: 1 1 320px;
    min-width: 300px;
    max-width: 420px;
  }

  .location-list {
    gap: 2rem;
  }

  .location-card {
    min-width: 280px;
    max-width: 350px;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .about-container {
    padding: 2.5rem 2rem 3.5rem 2rem;
    max-width: 1100px;
  }

  .vision-mission {
    gap: 2.5rem;
    margin-bottom: 3rem;
  }

  .vision-card, .mission-card {
    flex: 1 1 350px;
    min-width: 320px;
    max-width: 450px;
  }

  .location-list {
    gap: 2.5rem;
  }

  .location-card {
    min-width: 300px;
    max-width: 380px;
    padding: 1.25rem 1.5rem;
  }

  .location-card mat-icon {
    font-size: 2.25rem;
  }

  .loc-name {
    font-size: 1.05rem;
  }

  .loc-address {
    font-size: 1rem;
  }
}
