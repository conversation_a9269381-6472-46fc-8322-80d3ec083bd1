import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AdminAuthService {
  private isLoggedInSubject = new BehaviorSubject<boolean>(false);
  public isLoggedIn$ = this.isLoggedInSubject.asObservable();
  public user$ = this.afAuth.authState;

  constructor(private afAuth: AngularFireAuth) {
    // Listen to authentication state changes
    this.afAuth.authState.subscribe(user => {
      const isAuthenticated = !!user;
      this.isLoggedInSubject.next(isAuthenticated);
    });
  }

  // Firebase email/password login
  async loginWithEmailPassword(email: string, password: string): Promise<boolean> {
    try {
      const result = await this.afAuth.signInWithEmailAndPassword(email, password);
      if (result.user) {
        this.isLoggedInSubject.next(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      this.isLoggedInSubject.next(false);
      return false;
    }
  }

  // Legacy hardcoded login (keeping for backward compatibility)
  login(username: string, password: string): boolean {
    // For demo purposes, convert username/password to email format
    if (username === 'Admin' && password === 'password@123') {
      // You can replace this with actual Firebase login
      // For now, we'll use a demo email
      this.loginWithEmailPassword('<EMAIL>', 'password@123');
      return true;
    }
    return false;
  }

  async logout(): Promise<void> {
    try {
      await this.afAuth.signOut();
      this.isLoggedInSubject.next(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  isAuthenticated(): Observable<boolean> {
    return this.afAuth.authState.pipe(
      map(user => !!user)
    );
  }

  // Synchronous version for guard compatibility
  isAuthenticatedSync(): boolean {
    return this.isLoggedInSubject.value;
  }

  // Get current user
  getCurrentUser(): Observable<any> {
    return this.afAuth.authState;
  }

  // Get current user email
  getCurrentUserEmail(): Observable<string | null> {
    return this.afAuth.authState.pipe(
      map(user => user ? user.email : null)
    );
  }
}
