<div class="products-container" [@fadeIn]>
  <h1 class="mat-display-1">Our Products</h1>
  <div class="product-grid">
    <mat-card class="product-card" *ngFor="let product of products">
      <img
        mat-card-image
        [src]="product.imageUrl"
        [alt]="product.name"
        (error)="onImageError($event, product)"
        loading="lazy" />
      <mat-card-title>{{ product.name }}</mat-card-title>
      <mat-card-content>
        <p>{{ product.caption }}</p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
