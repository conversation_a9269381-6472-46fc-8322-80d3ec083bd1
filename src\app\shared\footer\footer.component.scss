.footer-bar {
  width: 100%;
  text-align: center;
  padding: 0.5rem 1rem 0.3rem 1rem;
  background: transparent !important;
  color: #b0b0b0 !important;
  font-size: 0.92rem;
  letter-spacing: 0.5px;
  border-top: none !important;
  min-height: unset;
  box-sizing: border-box;
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .footer-bar {
    padding: 0.4rem 0.75rem 0.25rem 0.75rem;
    font-size: 0.8rem;
    letter-spacing: 0.3px;
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .footer-bar {
    padding: 0.45rem 1rem 0.275rem 1rem;
    font-size: 0.85rem;
    letter-spacing: 0.4px;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .footer-bar {
    padding: 0.5rem 1.25rem 0.3rem 1.25rem;
    font-size: 0.9rem;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .footer-bar {
    padding: 0.55rem 1.5rem 0.325rem 1.5rem;
    font-size: 0.95rem;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .footer-bar {
    padding: 0.6rem 1.5rem 0.35rem 1.5rem;
    font-size: 1rem;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .footer-bar {
    padding: 0.65rem 2rem 0.4rem 2rem;
    font-size: 1.05rem;
    letter-spacing: 0.6px;
  }
}
