/* You can add global styles to this file, and also import other style files */

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden; // Prevent horizontal scroll
}

body {
  background: #181a20;
  color: #e0e0e0;
  font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
  color: #fff;
}

.mat-toolbar {
  background: #23272f !important;
  color: #fff !important;
}

.mat-card {
  background: #23272f !important;
  color: #e0e0e0 !important;
  box-shadow: 0 2px 12px rgba(0,0,0,0.25) !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border: none !important;
  color: transparent !important;
}

.mat-input-element, .mat-form-field-label {
  color: #90caf9 !important;
}

.mat-raised-button, .mat-button {
  background: #1976d2 !important;
  color: #fff !important;
}

.mat-raised-button.mat-accent {
  background: #90caf9 !important;
  color: #23272f !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick {
  border: none !important;
  color: transparent !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border: none !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-start,
.mat-form-field-appearance-outline .mat-form-field-outline-end {
  border: none !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline-gap {
  border: none !important;
}

.mat-form-field.mat-focused .mat-form-field-label {
  color: #90caf9 !important;
}

.mat-form-field.mat-focused .mat-form-field-ripple {
  background-color: #90caf9 !important;
}

footer, .footer-bar {
  background: #23272f !important;
  color: #b0b0b0 !important;
  border-top: 1px solid #222 !important;
}

// Remove background from .home-content if present
.home-content {
  background: none !important;
  box-shadow: none !important;
}

// Additional Material Design form field styles for dark theme
.mat-mdc-form-field {
  .mdc-text-field--filled {
    background-color: #23272f !important;
  }

  .mdc-text-field--outlined {
    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border: none !important;
        border-width: 0 !important;
      }
    }
  }
}

.mat-mdc-input-element {
  color: #90caf9 !important;
  caret-color: #90caf9 !important;
}

.mat-mdc-form-field-label {
  color: #90caf9 !important;
}

.mat-mdc-form-field-hint {
  color: rgba(144, 202, 249, 0.7) !important;
}

.mat-mdc-form-field-error {
  color: #ff8a80 !important;
}

// Global placeholder styles
.mat-mdc-input-element::placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element::-webkit-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element::-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element:-ms-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-mdc-input-element:-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

// Legacy Material placeholder styles
.mat-input-element::placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-input-element::-webkit-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-input-element::-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-input-element:-ms-input-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

.mat-input-element:-moz-placeholder {
  color: #ffffff !important;
  opacity: 0.8 !important;
}
