<div class="about-container" [@fadeIn]>
  <h1 class="mat-display-1">Our Story</h1>

  <section class="timeline">
    <mat-card class="timeline-card" *ngFor="let event of timeline">
      <div class="timeline-dot"></div>
      <div class="timeline-content">
        <h2 class="mat-h2">{{ event.title }}</h2>
        <p>{{ event.text }}</p>
      </div>
    </mat-card>
  </section>

  <section class="vision-mission">
    <mat-card class="vision-card">
      <mat-card-title>Our Vision</mat-card-title>
      <mat-card-content>
        <p>To become the most trusted and innovative footwear brand, inspiring confidence in every step.</p>
      </mat-card-content>
    </mat-card>
    <mat-card class="mission-card">
      <mat-card-title>Our Mission</mat-card-title>
      <mat-card-content>
        <p>Delivering comfort, style, and quality to every customer, while supporting sustainable and ethical practices.</p>
      </mat-card-content>
    </mat-card>
  </section>

  <section class="locations">
    <h2 class="mat-h3">Our Locations</h2>
    <div class="location-list">
      <mat-card class="location-card" *ngFor="let loc of locations">
        <mat-icon>location_on</mat-icon>
        <div>
          <div class="loc-name">{{ loc.city }}</div>
          <div class="loc-address">{{ loc.address }}</div>
        </div>
      </mat-card>
    </div>
  </section>
</div>
