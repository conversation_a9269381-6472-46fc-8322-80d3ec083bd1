import { Component, OnInit } from '@angular/core';
import { ProductService, Product } from '../../shared/product.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(40px)' }),
        animate('700ms 100ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ])
  ]
})
export class ProductsComponent implements OnInit {
  products: Product[] = [];

  constructor(private productService: ProductService) {}

  ngOnInit() {
    this.productService.getProducts().subscribe(products => {
      this.products = products;
    });
  }

  onImageError(event: any, product: Product) {
    console.error(`Failed to load image for ${product.name}:`, product.imageUrl);
    console.error('Error event:', event);
    // Set a fallback image or placeholder
    event.target.src = '/assets/images/placeholder.jpg'; // You can add a placeholder image
  }
}
