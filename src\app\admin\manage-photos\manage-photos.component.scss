.admin-container {
  max-width: 1300px;
  margin: 0 auto;
  padding: 2rem 1rem;
  background: #181a20;
  min-height: calc(100vh - 64px - 40px);
  width: 100%;
  box-sizing: border-box;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(144, 202, 249, 0.2);

  h1 {
    color: #90caf9;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.add-button {
  background: #90caf9 !important;
  color: #181a20 !important;
  font-weight: 600;

  mat-icon {
    margin-right: 0.5rem;
  }
}

.logout-button {
  color: #ff8a80 !important;

  mat-icon {
    margin-right: 0.5rem;
  }
}

.form-container {
  margin-bottom: 3rem;
}

.form-card {
  background: #23272f !important;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(144, 202, 249, 0.1);

  mat-card-title {
    color: #90caf9;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.product-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.form-row {
  width: 100%;
}

.full-width {
  width: 100%;
}

.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-upload-label {
  color: #90caf9;
  font-weight: 500;
  font-size: 1rem;
}

.file-input {
  display: none;
}

.upload-button {
  border-color: #90caf9 !important;
  color: #90caf9 !important;
  width: fit-content;

  mat-icon {
    margin-right: 0.5rem;
  }
}

.image-preview {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  object-fit: cover;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;

  button[type="submit"] {
    background: #90caf9 !important;
    color: #181a20 !important;
    font-weight: 600;
  }
}

.products-section {
  h2 {
    color: #90caf9;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.product-card {
  background: #23272f !important;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(144, 202, 249, 0.1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(144, 202, 249, 0.15);
  }
}

.product-image-container {
  height: 200px;
  overflow: hidden;
  background: #181a20;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.product-content {
  padding: 1rem;

  .product-name {
    color: #90caf9;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .product-caption {
    color: #b0b0b0;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
  }
}

.product-actions {
  padding: 0.5rem 1rem 1rem 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;

  button {
    &[color="primary"] {
      color: #90caf9 !important;
    }

    &[color="warn"] {
      color: #ff8a80 !important;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #b0b0b0;

  .empty-icon {
    font-size: 4rem;
    color: rgba(144, 202, 249, 0.3);
    margin-bottom: 1rem;
  }

  h3 {
    color: #90caf9;
    margin-bottom: 0.5rem;
  }

  p {
    margin: 0;
  }
}

/* Mobile Phones (4-7 inches) */
@media (max-width: 480px) {
  .admin-container {
    padding: 1rem 0.75rem;
    min-height: calc(100vh - 120px - 35px);
  }

  .admin-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;

    h1 {
      font-size: 1.3rem;
    }
  }

  .header-actions {
    width: 100%;
    flex-direction: column;
    gap: 0.75rem;

    .add-button, .logout-button {
      width: 100%;
      justify-content: center;
    }
  }

  .form-container {
    margin-bottom: 2rem;
  }

  .form-card {
    margin: 0;
    border-radius: 8px;
  }

  .product-form {
    gap: 1.25rem;
  }

  .preview-image {
    max-width: 150px;
    max-height: 150px;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;

    button {
      width: 100%;
    }
  }

  .products-section h2 {
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .product-image-container {
    height: 160px;
  }

  .product-content {
    padding: 0.75rem;

    .product-name {
      font-size: 1rem;
    }

    .product-caption {
      font-size: 0.85rem;
    }
  }

  .product-actions {
    padding: 0.5rem 0.75rem 0.75rem 0.75rem;
    flex-direction: column;
    gap: 0.5rem;

    button {
      width: 100%;
    }
  }
}

/* Small Mobile and Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
  .admin-container {
    padding: 1.25rem 1rem;
    min-height: calc(100vh - 100px - 35px);
  }

  .admin-header {
    flex-direction: column;
    gap: 1.25rem;
    align-items: flex-start;
    margin-bottom: 1.75rem;

    h1 {
      font-size: 1.4rem;
    }
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
    gap: 1rem;
  }

  .preview-image {
    max-width: 175px;
    max-height: 175px;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
    }
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
  }

  .product-image-container {
    height: 180px;
  }
}

/* Tablets */
@media (min-width: 601px) and (max-width: 768px) {
  .admin-container {
    padding: 1.5rem 1.25rem;
    min-height: calc(100vh - 80px - 40px);
  }

  .admin-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
    margin-bottom: 2rem;

    h1 {
      font-size: 1.6rem;
    }
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
    gap: 1.25rem;
  }

  .preview-image {
    max-width: 200px;
    max-height: 200px;
  }

  .form-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;

    button {
      flex: 1;
      min-width: 120px;
    }
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
  }
}

/* iPads and Small Laptops */
@media (min-width: 769px) and (max-width: 1024px) {
  .admin-container {
    padding: 1.75rem 1.5rem;
    max-width: 1100px;
  }

  .admin-header {
    margin-bottom: 2.25rem;

    h1 {
      font-size: 1.8rem;
    }
  }

  .header-actions {
    gap: 1.5rem;
  }

  .form-container {
    margin-bottom: 2.75rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.75rem;
  }

  .product-image-container {
    height: 200px;
  }
}

/* Laptops */
@media (min-width: 1025px) and (max-width: 1440px) {
  .admin-container {
    padding: 2rem 1.5rem;
    max-width: 1200px;
  }

  .admin-header {
    margin-bottom: 2.5rem;

    h1 {
      font-size: 2rem;
    }
  }

  .header-actions {
    gap: 1.75rem;
  }

  .form-container {
    margin-bottom: 3rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 2rem;
  }
}

/* Large Desktops */
@media (min-width: 1441px) {
  .admin-container {
    padding: 2.5rem 2rem;
    max-width: 1400px;
  }

  .admin-header {
    margin-bottom: 3rem;

    h1 {
      font-size: 2.25rem;
    }
  }

  .header-actions {
    gap: 2rem;
  }

  .form-container {
    margin-bottom: 3.5rem;
  }

  .preview-image {
    max-width: 250px;
    max-height: 250px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2.5rem;
  }

  .product-image-container {
    height: 220px;
  }

  .product-content {
    padding: 1.25rem;

    .product-name {
      font-size: 1.2rem;
    }

    .product-caption {
      font-size: 0.95rem;
    }
  }

  .product-actions {
    padding: 0.75rem 1.25rem 1.25rem 1.25rem;
  }
}
