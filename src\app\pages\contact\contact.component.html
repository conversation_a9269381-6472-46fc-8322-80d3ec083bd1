<div class="contact-container" [@fadeIn]>
  <h1>Contact Us</h1>

  <!-- Success/Error Message -->
  <div *ngIf="submitMessage" class="message-container" [ngClass]="submitMessageType">
    <mat-icon>{{ submitMessageType === 'success' ? 'check_circle' : 'error' }}</mat-icon>
    <span>{{ submitMessage }}</span>
    <button mat-icon-button (click)="clearMessage()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <form [formGroup]="contactForm" class="contact-form" autocomplete="off" (ngSubmit)="onSubmit()">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Name</mat-label>
      <input matInput formControlName="name" maxlength="50" required [disabled]="isLoading">
      <mat-error *ngIf="contactForm.get('name')?.hasError('required')">Name is required</mat-error>
      <mat-hint align="end">{{ contactForm.get('name')?.value?.length || 0 }}/50</mat-hint>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input matInput formControlName="email" required [disabled]="isLoading">
      <mat-error *ngIf="contactForm.get('email')?.hasError('required')">Email is required</mat-error>
      <mat-error *ngIf="contactForm.get('email')?.hasError('email')">Enter a valid email</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Your Query</mat-label>
      <textarea matInput formControlName="query" rows="5" maxlength="500" required [disabled]="isLoading"></textarea>
      <mat-error *ngIf="contactForm.get('query')?.hasError('required')">Query is required</mat-error>
      <mat-hint align="end">{{ contactForm.get('query')?.value?.length || 0 }}/500</mat-hint>
    </mat-form-field>

    <button
      mat-raised-button
      color="primary"
      type="submit"
      [disabled]="!contactForm.valid || isLoading"
      class="submit-button">
      <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
      <span *ngIf="!isLoading">Send Message</span>
      <span *ngIf="isLoading">Sending...</span>
    </button>
  </form>
</div>
