<div class="contact-container" [@fadeIn]>
  <h1>Contact Us</h1>
  <form [formGroup]="contactForm" class="contact-form" autocomplete="off">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Name</mat-label>
      <input matInput formControlName="name" maxlength="50" required>
      <mat-error *ngIf="contactForm.get('name')?.hasError('required')">Name is required</mat-error>
      <mat-hint align="end">{{ contactForm.get('name')?.value?.length || 0 }}/50</mat-hint>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input matInput formControlName="email" required>
      <mat-error *ngIf="contactForm.get('email')?.hasError('required')">Email is required</mat-error>
      <mat-error *ngIf="contactForm.get('email')?.hasError('email')">Enter a valid email</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Your Query</mat-label>
      <textarea matInput formControlName="query" rows="5" maxlength="500" required></textarea>
      <mat-error *ngIf="contactForm.get('query')?.hasError('required')">Query is required</mat-error>
      <mat-hint align="end">{{ contactForm.get('query')?.value?.length || 0 }}/500</mat-hint>
    </mat-form-field>

    <button mat-raised-button color="primary" type="submit" [disabled]="!contactForm.valid">Send</button>
  </form>
</div>
